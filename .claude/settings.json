{"env": {"NODE_ENV": "development"}, "includeCoAuthoredBy": false, "permissions": {"allow": ["LS", "cat:*", "ls:*", "grep:*", "find:*", "GlobTool", "GrepTool", "Read", "pwd", "NotebookRead", "Bash(npm run lint)", "Bash(npm run test:*)", "Bash(git status)", "Bash(git diff:*)", "Bash(git commit:*)", "MCP__your_tool_name", "Edit(**)", "Write(**)", "EditFile:*", "edit_file"], "deny": ["Bash(rm -rf *)", "<PERSON><PERSON>(curl *)", "<PERSON><PERSON>(wget *)", "Bash(ssh *)", "<PERSON><PERSON>(sudo *)", "Write(/etc/**)", "Write(~/.ssh/**)"]}}